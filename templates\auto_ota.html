{% extends "base.html" %}

{% block title %}自动OTA升级管理{% endblock %}

{% block styles %}
<style>
.form-switch .form-check-input {
    width: 3em;
    height: 1.5em;
    margin-right: 0.5rem;
    transform: scale(1.2);
    margin-top: 0.1em;
}

.form-switch .form-check-input:checked {
    background-color: #198754;
    border-color: #198754;
}

.form-switch .form-check-input:focus {
    box-shadow: 0 0 0 0.25rem rgba(25, 135, 84, 0.25);
}

.card {
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
}


</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- 页面标题 -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h3>自动OTA升级管理</h3>
                <a href="{{ url_for('firmware.latest_firmware_management') }}" class="btn btn-outline-primary">
                    <i class="fas fa-cog"></i> 最新固件配置
                </a>
            </div>

            <!-- 服务状态 -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-server fa-2x text-info mb-2"></i>
                            <h6 class="card-title">服务状态</h6>
                            <span id="service-status" class="badge bg-secondary">检查中...</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-toggle-on fa-2x text-success mb-2"></i>
                            <h6 class="card-title">自动升级</h6>
                            <span id="auto-upgrade-status" class="badge bg-secondary">检查中...</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                            <h6 class="card-title">超时时间</h6>
                            <span id="current-timeout" class="fw-bold">-</span> 秒
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="fas fa-redo fa-2x text-danger mb-2"></i>
                            <h6 class="card-title">最大重试</h6>
                            <span id="current-retries" class="fw-bold">-</span> 次
                        </div>
                    </div>
                </div>
            </div>

            <!-- 配置设置 -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">配置设置</h5>
                </div>
                <div class="card-body">
                    <div class="row g-4">
                        <!-- 基础开关 -->
                        <div class="col-md-6">
                            <div class="row g-3">
                                <div class="col-12">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="enabled" name="enabled">
                                        <label class="form-check-label fw-bold" for="enabled">
                                            启用自动OTA升级
                                        </label>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="force_update" name="force_update">
                                        <label class="form-check-label fw-bold" for="force_update">
                                            强制更新模式
                                        </label>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="compare_version" name="compare_version">
                                        <label class="form-check-label fw-bold" for="compare_version">
                                            比较版本号
                                        </label>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="simulation_mode" name="simulation_mode">
                                        <label class="form-check-label fw-bold" for="simulation_mode">
                                            模拟观察模式
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- 数值配置 -->
                        <div class="col-md-6">
                            <div class="row g-3">
                                <div class="col-12">
                                    <label for="timeout" class="form-label">超时时间（秒）</label>
                                    <input type="number" class="form-control" id="timeout" name="timeout"
                                           min="60" max="3600" placeholder="300">
                                    <div class="form-text">范围：60-3600秒</div>
                                </div>
                                <div class="col-12">
                                    <label for="max_retries" class="form-label">最大重试次数</label>
                                    <input type="number" class="form-control" id="max_retries" name="max_retries"
                                           min="0" max="10" placeholder="3">
                                    <div class="form-text">范围：0-10次</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>

<!-- 确认模态框 -->
<div class="modal fade" id="confirmModal" tabindex="-1" aria-labelledby="confirmModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmModalLabel">确认配置更改</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>您确定要保存以下配置更改吗？</p>
                <div id="config-summary"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirm-save">确认保存</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    let pendingConfig = null;
    let originalValues = {};

    // 页面加载时获取状态和配置
    loadStatus();
    loadConfig();

    // 开关变化时立即显示确认模态框
    $('#enabled, #force_update, #compare_version, #simulation_mode').change(function() {
        const $this = $(this);
        // 保存当前状态作为待确认状态
        showConfigConfirmModal();
    });

    // 输入框变化时显示确认模态框
    $('#timeout, #max_retries').on('blur', function() {
        if ($(this).val() && $(this).val() !== $(this).data('original-value')) {
            showConfigConfirmModal();
        }
    });

    // 确认保存按钮
    $('#confirm-save').click(function() {
        if (pendingConfig) {
            saveConfig(pendingConfig);
            $('#confirmModal').modal('hide');
        }
    });

    // 取消按钮 - 恢复原状态
    $('#confirmModal .btn-secondary, #confirmModal .btn-close').click(function() {
        restoreOriginalValues();
    });

    // 模态框关闭时恢复原状态（如果没有保存）
    $('#confirmModal').on('hidden.bs.modal', function() {
        if (pendingConfig) {
            restoreOriginalValues();
            pendingConfig = null;
        }
    });

    function loadStatus() {
        $.get('/api/auto_ota/status')
            .done(function(response) {
                if (response.success) {
                    const status = response.status;
                    
                    // 更新服务状态
                    const serviceStatus = status.initialized ? '运行中' : '未初始化';
                    const serviceClass = status.initialized ? 'bg-success' : 'bg-danger';
                    $('#service-status').removeClass().addClass('badge ' + serviceClass).text(serviceStatus);

                    // 更新自动升级状态
                    const autoStatus = status.config.enabled ? '已启用' : '已禁用';
                    const autoClass = status.config.enabled ? 'bg-success' : 'bg-warning';
                    $('#auto-upgrade-status').removeClass().addClass('badge ' + autoClass).text(autoStatus);
                    
                    // 更新当前配置显示
                    $('#current-timeout').text(status.config.timeout);
                    $('#current-retries').text(status.config.max_retries);
                }
            })
            .fail(function() {
                $('#service-status').removeClass().addClass('badge bg-danger').text('获取失败');
                $('#auto-upgrade-status').removeClass().addClass('badge bg-danger').text('获取失败');
            });
    }

    function loadConfig() {
        $.get('/api/auto_ota/config')
            .done(function(response) {
                if (response.success) {
                    const config = response.config;
                    $('#enabled').prop('checked', config.enabled);
                    $('#force_update').prop('checked', config.force_update);
                    $('#compare_version').prop('checked', config.compare_version);
                    $('#simulation_mode').prop('checked', config.simulation_mode);
                    $('#timeout').val(config.timeout).data('original-value', config.timeout);
                    $('#max_retries').val(config.max_retries).data('original-value', config.max_retries);

                    // 保存原始值
                    originalValues = {
                        enabled: config.enabled,
                        force_update: config.force_update,
                        compare_version: config.compare_version,
                        simulation_mode: config.simulation_mode,
                        timeout: config.timeout,
                        max_retries: config.max_retries
                    };
                }
            })
            .fail(function() {
                showAlert('获取配置失败', 'danger');
            });
    }

    function restoreOriginalValues() {
        $('#enabled').prop('checked', originalValues.enabled);
        $('#force_update').prop('checked', originalValues.force_update);
        $('#compare_version').prop('checked', originalValues.compare_version);
        $('#simulation_mode').prop('checked', originalValues.simulation_mode);
        $('#timeout').val(originalValues.timeout);
        $('#max_retries').val(originalValues.max_retries);
    }

    function showConfigConfirmModal() {
        pendingConfig = {
            enabled: $('#enabled').is(':checked'),
            force_update: $('#force_update').is(':checked'),
            compare_version: $('#compare_version').is(':checked'),
            simulation_mode: $('#simulation_mode').is(':checked'),
            timeout: parseInt($('#timeout').val()) || 300,
            max_retries: parseInt($('#max_retries').val()) || 3
        };

        // 验证输入
        if (pendingConfig.timeout < 60 || pendingConfig.timeout > 3600) {
            showAlert('超时时间必须在60-3600秒之间', 'warning');
            return;
        }

        if (pendingConfig.max_retries < 0 || pendingConfig.max_retries > 10) {
            showAlert('最大重试次数必须在0-10次之间', 'warning');
            return;
        }

        // 生成配置摘要
        const summary = `
            <ul class="list-unstyled">
                <li><strong>自动升级：</strong>${pendingConfig.enabled ? '启用' : '禁用'}</li>
                <li><strong>强制更新：</strong>${pendingConfig.force_update ? '启用' : '禁用'}</li>
                <li><strong>比较版本号：</strong>${pendingConfig.compare_version ? '启用' : '禁用'}</li>
                <li><strong>模拟观察模式：</strong>${pendingConfig.simulation_mode ? '启用' : '禁用'}</li>
                <li><strong>超时时间：</strong>${pendingConfig.timeout} 秒</li>
                <li><strong>最大重试：</strong>${pendingConfig.max_retries} 次</li>
            </ul>
        `;
        $('#config-summary').html(summary);
        $('#confirmModal').modal('show');
    }

    function saveConfig(config) {
        $.ajax({
            url: '/api/auto_ota/config',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(config)
        })
        .done(function(response) {
            if (response.success) {
                showAlert('配置保存成功', 'success');
                loadStatus(); // 刷新状态显示
                loadConfig(); // 刷新配置显示
                pendingConfig = null; // 清除待确认配置
            } else {
                showAlert('配置保存失败: ' + response.message, 'danger');
                restoreOriginalValues(); // 保存失败时恢复原值
            }
        })
        .fail(function() {
            showAlert('配置保存失败', 'danger');
            restoreOriginalValues(); // 保存失败时恢复原值
        });
    }


});
</script>
{% endblock %}
